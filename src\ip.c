#include "ip.h"

#include "arp.h"
#include "ethernet.h"
#include "icmp.h"
#include "net.h"

/**
 * @brief 处理一个收到的数据包
 *
 * @param buf 要处理的数据包
 * @param src_mac 源mac地址
 */
void ip_in(buf_t *buf, uint8_t *src_mac) {
    // TO-DO
    if(buf->len < sizeof(ip_hdr_t))
      return;
    ip_hdr_t* ip = (ip_hdr_t*)buf->data;

    if(ip->version != IP_VERSION_4)
      return;
    if(swap16(ip->total_len16) > buf->len)
      return;

    uint16_t oldchecksum = ip->hdr_checksum16;
    ip->hdr_checksum16 = 0;
    uint16_t newchecksum = checksum16((uint16_t*)ip, sizeof(ip_hdr_t));
    if(oldchecksum != newchecksum)
      return;
    ip->hdr_checksum16 = oldchecksum;

    uint8_t ipprotocol = ip->protocol;
    uint8_t srcip[NET_IP_LEN];
    memcpy(srcip, ip->src_ip, NET_IP_LEN);
    if(memcmp(ip->dst_ip, net_if_ip, NET_IP_LEN) != 0)
      return;

    if(buf->len > swap16(ip->total_len16))
      buf_remove_padding(buf, buf->len - swap16(ip->total_len16));
    buf_remove_header(buf, sizeof(ip_hdr_t));
    if(net_in(buf, ipprotocol, srcip) < 0){
      buf_add_header(buf, sizeof(ip_hdr_t));
      icmp_unreachable(buf, srcip, ICMP_CODE_PROTOCOL_UNREACH);
    }
}
/**
 * @brief 处理一个要发送的ip分片
 *
 * @param buf 要发送的分片
 * @param ip 目标ip地址
 * @param protocol 上层协议
 * @param id 数据包id
 * @param offset 分片offset，必须被8整除
 * @param mf 分片mf标志，是否有下一个分片
 */
void ip_fragment_out(buf_t *buf, uint8_t *ip, net_protocol_t protocol, int id, uint16_t offset, int mf) {
    // TO-DO
    buf_add_header(buf, sizeof(ip_hdr_t));
    ip_hdr_t *iphdr = (ip_hdr_t*)buf->data;
    iphdr->version = IP_VERSION_4;
    iphdr->hdr_len = sizeof(ip_hdr_t) / 4;
    iphdr->tos = 0;
    iphdr->total_len16 = swap16(buf->len);
    iphdr->id16 = swap16(id);
    uint16_t flags_offset_val = offset / IP_HDR_OFFSET_PER_BYTE;
    if(mf)
      flags_offset_val |= IP_MORE_FRAGMENT;
    iphdr->flags_fragment16 = swap16(flags_offset_val);
    iphdr->ttl = IP_DEFALUT_TTL;
    iphdr->protocol = protocol;
    memcpy(iphdr->src_ip, net_if_ip, NET_IP_LEN);
    memcpy(iphdr->dst_ip, ip, NET_IP_LEN);
    iphdr->hdr_checksum16 = 0;
    iphdr->hdr_checksum16 = checksum16((uint16_t*)iphdr, sizeof(ip_hdr_t));
    arp_out(buf, ip);
}

/**
 * @brief 处理一个要发送的ip数据包
 *
 * @param buf 要处理的包
 * @param ip 目标ip地址
 * @param protocol 上层协议
 */
void ip_out(buf_t *buf, uint8_t *ip, net_protocol_t protocol) {
    // TO-DO
    static int id = 0;
    size_t max_len = ETHERNET_MAX_TRANSPORT_UNIT - sizeof(ip_hdr_t);
    
    if(buf->len <= max_len) {
        ip_fragment_out(buf, ip, protocol, id++, 0, 0);
        return;
    }
    uint16_t offset = 0;
    char *data_ptr = (char *)buf->data;
    size_t remain_len = buf->len;
    
    while (remain_len > 0) {
      size_t current_len = (remain_len > max_len) ? max_len : remain_len;
      buf_t ip_buf;
      buf_init(&ip_buf, current_len);
      memcpy(ip_buf.data, data_ptr, current_len);
      int mf = (remain_len > max_len);
      ip_fragment_out(&ip_buf, ip, protocol, id, offset, mf);
      data_ptr += current_len;
      offset += current_len;
      remain_len -= current_len;
    }
    id++;
}

/**
 * @brief 初始化ip协议
 *
 */
void ip_init() {
    net_add_protocol(NET_PROTOCOL_IP, ip_in);
}