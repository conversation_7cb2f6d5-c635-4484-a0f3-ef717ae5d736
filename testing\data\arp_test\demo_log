driver opened
<====== arp table =======>
<====== arp buf =======>

Round 01 -----------------------------
<====== arp table =======>
<====== arp buf =======>
************** ->  45 00 00 46 fb 7c 40 00 40 11 77 67 c0 a8 a3 67 c0 a8 a3 0a ae 1b 00 35 00 32 79 68 96 da 01 00 00 01 00 00 00 00 00 01 03 77 77 77 05 62 61 69 64 75 03 63 6f 6d 00 00 01 00 01 00 00 29 02 00 00 00 00 00 00 00

Round 02 -----------------------------
<====== arp table =======>
************** -> 21:32:43:54:65:06
<====== arp buf =======>

Round 03 -----------------------------
<====== arp table =======>
************** -> 21:32:43:54:65:06
<====== arp buf =======>

Round 04 -----------------------------
ip_in:
	mac:21:32:43:54:65:06
	buf: 45 00 00 9a 88 e4 00 00 40 11 20 ac c0 a8 a3 0a c0 a8 a3 67 00 35 bf 6a 00 86 4b a8 97 59 81 80 00 01 00 01 00 01 00 01 03 77 77 77 05 62 61 69 64 75 03 63 6f 6d 00 00 1c 00 01 c0 0c 00 05 00 01 00 00 04 40 00 0f 03 77 77 77 01 61 06 73 68 69 66 65 6e c0 16 c0 2f 00 06 00 01 00 00 00 21 00 2d 03 6e 73 31 c0 2f 10 62 61 69 64 75 5f 64 6e 73 5f 6d 61 73 74 65 72 c0 10 77 d0 4d 62 00 00 00 05 00 00 00 05 00 27 8d 00 00 00 0e 10 00 00 29 10 00 00 00 00 00 00 00
<====== arp table =======>
************** -> 21:32:43:54:65:06
<====== arp buf =======>

Round 05 -----------------------------
ip_in:
	mac:21:32:43:54:65:06
	buf: 45 00 00 81 88 e5 00 00 40 11 29 c4 c0 a8 a3 0a c0 a8 a3 67 00 35 ae 1b 00 6d bb f0 96 da 81 80 00 01 00 03 00 00 00 01 03 77 77 77 05 62 61 69 64 75 03 63 6f 6d 00 00 01 00 01 c0 0c 00 05 00 01 00 00 00 ec 00 0f 03 77 77 77 01 61 06 73 68 69 66 65 6e c0 16 c0 2b 00 01 00 01 00 00 00 0b 00 04 b7 e8 e7 ae c0 2b 00 01 00 01 00 00 00 0b 00 04 b7 e8 e7 ac 00 00 29 10 00 00 00 00 00 00 00
<====== arp table =======>
************** -> 21:32:43:54:65:06
<====== arp buf =======>

Round 06 -----------------------------
<====== arp table =======>
************** -> 21:32:43:54:65:06
<====== arp buf =======>

Round 07 -----------------------------
ip_in:
	mac:21:32:43:54:65:06
	buf: 45 00 00 88 88 e6 00 00 40 11 29 bc c0 a8 a3 0a c0 a8 a3 67 00 35 84 9f 00 74 72 81 5a 54 81 80 00 01 00 00 00 01 00 01 03 77 77 77 01 61 06 73 68 69 66 65 6e 03 63 6f 6d 00 00 1c 00 01 c0 10 00 06 00 01 00 00 01 23 00 33 03 6e 73 31 c0 10 10 62 61 69 64 75 5f 64 6e 73 5f 6d 61 73 74 65 72 05 62 61 69 64 75 c0 19 77 d0 4d 62 00 00 00 05 00 00 00 05 00 27 8d 00 00 00 0e 10 00 00 29 10 00 00 00 00 00 00 00
<====== arp table =======>
************** -> 21:32:43:54:65:06
<====== arp buf =======>

Round 08 -----------------------------
ip_in:
	mac:01:12:23:34:45:56
	buf: 45 00 00 54 01 f4 40 00 40 01 70 8e c0 a8 a3 6e c0 a8 a3 67 08 00 3b 6a 00 01 00 01 c8 e4 86 5f 00 00 00 00 ae 7c 00 00 00 00 00 00 10 11 12 13 14 15 16 17 18 19 1a 1b 1c 1d 1e 1f 20 21 22 23 24 25 26 27 28 29 2a 2b 2c 2d 2e 2f 30 31 32 33 34 35 36 37
<====== arp table =======>
************** -> 21:32:43:54:65:06
<====== arp buf =======>

Round 09 -----------------------------
<====== arp table =======>
************** -> 21:32:43:54:65:06
*************** -> 01:12:23:34:45:56
<====== arp buf =======>

Round 10 -----------------------------
<====== arp table =======>
************** -> 21:32:43:54:65:06
*************** -> 01:12:23:34:45:56
************* -> 1a:94:f0:3c:49:aa
<====== arp buf =======>

Round 11 -----------------------------
<====== arp table =======>
************** -> 21:32:43:54:65:06
*************** -> 01:12:23:34:45:56
************* -> 1a:94:f0:3c:49:aa
<====== arp buf =======>

Round 12 -----------------------------
ip_in:
	mac:1a:94:f0:3c:49:aa
	buf: 45 00 00 5c 88 ea 00 00 40 06 29 f7 c0 a8 a3 02 c0 a8 a3 67 fb 21 00 16 22 ea f8 ef 4f 43 b1 3b 50 18 ff ff 04 1f 00 00 20 6d 88 68 18 ca 68 85 f0 82 62 4e ce bd 22 52 23 9e ea c9 af 8d 98 ed c4 fb 0e 56 ec 3d 1e bd 0d 0b 1c 5b f5 0a 25 38 73 24 ff 8f 79 54 f2 f3 97 71 1e 8a
<====== arp table =======>
************** -> 21:32:43:54:65:06
*************** -> 01:12:23:34:45:56
************* -> 1a:94:f0:3c:49:aa
<====== arp buf =======>

Round 13 -----------------------------
<====== arp table =======>
************** -> 21:32:43:54:65:06
*************** -> 01:12:23:34:45:56
************* -> 1a:94:f0:3c:49:aa
<====== arp buf =======>

Round 14 -----------------------------
<====== arp table =======>
************** -> 21:32:43:54:65:06
*************** -> 01:12:23:34:45:56
************* -> 1a:94:f0:3c:49:aa
<====== arp buf =======>

Round 15 -----------------------------
ip_in:
	mac:21:32:43:54:65:06
	buf: 45 00 00 28 89 0d 00 00 40 06 2a 00 c0 a8 a3 0a c0 a8 a3 67 00 50 d8 84 a5 e0 66 02 7f 53 e7 77 50 10 ff ff d0 c6 00 00 00 00 00 00 00 00
<====== arp table =======>
************** -> 21:32:43:54:65:06
*************** -> 01:12:23:34:45:56
************* -> 1a:94:f0:3c:49:aa
<====== arp buf =======>

driver closed
